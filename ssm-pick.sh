#!/bin/zsh
set -euo pipefail

PROFILE="${AWS_PROFILE:-}"
REGION="${AWS_REGION:-}"

while getopts ":p:r:h" opt; do
  case "$opt" in
    p) PROFILE="$OPTARG" ;;
    r) REGION="$OPTARG" ;;
    h|*) echo "Usage: $0 [-p profile] [-r region]"; exit 1 ;;
  esac
done

aws_cli=(aws)
[[ -n "$PROFILE" ]] && aws_cli+=("--profile" "$PROFILE")
[[ -n "$REGION"  ]] && aws_cli+=("--region"  "$REGION")

# Ensure jq is present
command -v jq >/dev/null || { echo "jq is required (brew install jq)"; exit 2; }

# 1) List SSM-managed instances
managed_ids="$("${aws_cli[@]}" ssm describe-instance-information \
  --query 'InstanceInformationList[].InstanceId' \
  --output text 2>/dev/null || true)"

if [[ -z "$managed_ids" ]]; then
  echo "No SSM-managed instances found."
  exit 0
fi

# 2) Get EC2 details
ec2_json="$("${aws_cli[@]}" ec2 describe-instances --output json)"

menu_json="$(jq --argjson m "$(jq -Rn --arg ids "$managed_ids" '$ids | split("\t") | map({key:., value:true}) | from_entries')" '
  .Reservations
  | map(.Instances[])
  | map(select($m[.InstanceId] == true))
  | map({
      InstanceId,
      Name: ((.Tags // []) | map(select(.Key=="Name")) | .[0].Value // "(no-name)"),
      State: .State.Name,
      PrivateIp: (.PrivateIpAddress // "-"),
      Az: .Placement.AvailabilityZone,
      Platform: (.PlatformDetails // "-")
    })
' <<<"$ec2_json")"

if [[ "$(jq 'length' <<<"$menu_json")" -eq 0 ]]; then
  echo "No EC2 details found for SSM-managed instances."
  exit 1
fi

lines=($(jq -r '.[] | "\(.InstanceId)"' <<<"$menu_json" | sort))

if [[ ${#lines[@]} -eq 0 ]]; then
  echo "No instances to display."
  exit 1
fi

# 3) Select instance
if command -v fzf >/dev/null; then
  pick_line="$(printf "%s\n" "${lines[@]}" | fzf --height=40% --reverse --prompt="Choose instance > ")"
else
  echo "Select an instance:"
  select choice in "${lines[@]}"; do
    [[ -n "$choice" ]] && pick_line="$choice" && break
    echo "Invalid selection."
  done
fi

[[ -z "$pick_line" ]] && { echo "No selection made."; exit 1; }
echo "$pick_line"
instance_id="$(awk -F'|' '{gsub(/^[ \t]+|[ \t]+$/,"",$2); print $2}' <<<"$pick_line")"

echo "Starting SSM session with $instance_id ..."
exec "${aws_cli[@]}" ssm start-session --target "$instance_id"